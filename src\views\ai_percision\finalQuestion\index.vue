<!-- 我的积分 -->
<template>
    <div class="content">
        <div class="header">
            <div class="header-img">
                <img src="@/assets/img/percision/back.png" alt="back"></img>
                <img src="@/assets/img/percision/final_text.png" alt="text"></img>
            </div>
            <div class="header-text">
                教材版本教材版本教材版本
            </div>
        </div>
        <div class="main">
            <img class="record-sty" src="@/assets/img/percision/record.png" alt="start"></img>
            <div class="main-box">
                <div class="main-box-flex">
                    <img class="way-start" src="@/assets/img/percision/final_start.png" alt="start"></img>
                    <div v-for="value in 10">
                        <div v-if="value == 1" class="way-box">
                            <img class="way-sty" src="@/assets/img/percision/way1.png" alt=""></img>
                            <div class="island-box position-right">
                                <img class="way-sty" :src="getIsland(value, 1)" alt=""></img>
                                <!-- <img class="way-status" src="@/assets/img/percision/final_success.png" alt=""></img> -->
                                <img class="way-status-chain" src="@/assets/img/percision/chain.png" alt=""></img>
                                <div class="way-rate-box">
                                    <img src="@/assets/img/percision/report.png" alt=""></img>
                                    正确率：<span class="way-rate">{{ "90.8" }}</span>%
                                </div>
                                <div class="way-point-box">
                                    <div class="way-point-box-cont">
                                        {{ value }}. 知识点名称知识点名称知识点名称
                                    </div>
                                    <div class="way-point-box-btn">
                                        <div>再次闯关</div>
                                        <div><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else-if="value % 2 === 0" class="way-box">
                            <img class="way-sty" src="@/assets/img/percision/way2.png" alt=""></img>
                            <div class="island-box position-left top50">
                                <img class="way-sty" :src="getIsland(value, 0)" alt=""></img>
                                <img class="way-status" src="@/assets/img/percision/final_fail.png" alt=""></img>
                                <div class="way-rate-box text-right">
                                    <img src="@/assets/img/percision/report.png" alt=""></img>
                                    正确率：<span class="way-rate">{{ "90.8" }}</span>%
                                </div>
                                <div class="way-point-box">
                                    <div class="way-point-box-cont">
                                        {{ value }}. 知识点名称知识点名称知识点名称
                                    </div>
                                    <div class="way-point-box-btn">
                                        <div>再次闯关</div>
                                        <div><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else-if="value % 2 !== 0" class="way-box">
                            <img class="way-sty" src="@/assets/img/percision/way3.png" alt=""></img>
                            <div class="island-box position-right top50">
                                <img class="way-sty" :src="getIsland(value, 1)" alt=""></img>
                                <img class="way-status" src="@/assets/img/percision/final_success.png" alt=""></img>
                                <div class="way-rate-box">
                                    <img src="@/assets/img/percision/report.png" alt=""></img>
                                    正确率：<span class="way-rate">{{ "90.8" }}</span>%
                                </div>
                                <div class="way-point-box">
                                    <div class="way-point-box-cont">
                                        {{ value }}. 知识点名称知识点名称知识点名称
                                    </div>
                                    <div class="way-point-box-btn">
                                        <div>再次闯关</div>
                                        <div><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
  
<script lang="ts" setup>

const getIsland = (value: number, status: number) => {
    const sign = value % 4
    let img = ""
    if (status == 1) {
        switch (sign) {
            case 0:
                img = "island1.png"
                break
            case 1:
                img = "island2.png"
                break
            case 2:
                img = "island3.png"
                break
            case 3:
                img = "island4.png"
                break
        }
    } else {
        switch (sign) {
            case 0:
                img = "island1_grey.png"
                break
            case 1:
                img = "island2_grey.png"
                break
            case 2:
                img = "island3_grey.png"
                break
            case 3:
                img = "island4_grey.png"
                break
        }
    }
    return new URL(`../../../assets/img/percision/${img}`, import.meta.url).href //静态资源引入为url，相当于require()

}
</script>
  
<style lang="scss" scoped>
.content{
    width: 100%;
    height: calc(100vh - 70px);
    background: url(@/assets/img/percision/finalbg.png) no-repeat;
    background-size: 100% calc(100vh - 70px);
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: column;
    .header {
        width: 1300px;
        height: 93px;
        display: flex;
        padding-top: 30px;
        box-sizing: border-box;
        justify-content: space-between;
        &-img {
            display: flex;
            align-items: center;
            img:first-child {
                width: 44px;
                height: 32px;
            }
            img:last-child {
                width: 205px;
                height: 53px;
            }
        }
        &-text {
            height: fit-content;
            border-radius: 22px;
            background: #0000004d;
            color: #ffffff;
            font-size: 16px;
            font-weight: 400;
            padding: 11px 20px;
        }
    }
    .main {
        width: 1300px;
        height: calc(100vh - 163px);
        background: #ffffff;
        padding: 20px 20px 0 20px;
        box-sizing: border-box;
        position: relative;
        .record-sty {
            position: absolute;
            bottom: 50px;
            right: 70px;
            width: 87px;
            height: 80px;
            cursor: pointer;
        }
        &-box {
            height: calc(100vh - 183px);
            width: 100%;
            background: url(@/assets/img/percision/finalbf2.png) no-repeat;
            background-size: 100% 100%;
            display: flex;
            overflow-y: auto;
            justify-content: center;
            .main-box-flex {
                width: 684px;
                margin-top: 10px;
                padding: 0 76px;
                box-sizing: border-box;
            }
            .way-start {
                width: 97px;
                height: 65px;
                margin-left: 26px;
                margin-bottom: -5px;
            }
            .way-box {
                position: relative;
                .way-sty {
                    width: 534px;
                }
                .island-box {
                    position: absolute;
                    top: 0;
                    .way-sty {
                        width: 200px;
                        height: 200px;
                        position: relative;
                        z-index: 10;
                    }
                    .way-status {
                        position: absolute;
                        top: -50px;
                        left: 0;
                        width: 200px;
                        height: 200px;
                        z-index: 11;
                    }
                    .way-status-chain {
                        position: absolute;
                        width: 180px;
                        left: 8px;
                        top: 60px;
                        z-index: 100;
                    }
                    .way-rate-box {
                        position: absolute;
                        z-index: 1;
                        width: 180px;
                        top: 90px;
                        right: 150px;
                        background-color: #ffffff;
                        padding: 6px 12px;
                        font-size: 14px;
                        border-radius: 16px;
                        display: flex;
                        align-items: center;
                        span {
                            font-weight: 700;
                        }
                        img {
                            width: 16px;
                            height: 16px;
                            margin-right: 5px;
                        }
                    }
                    .text-right {
                        left: 150px;
                        width: 120px;
                        padding-left: 60px;
                    }
                    .way-point-box {
                        position: absolute;
                        top: 143px;
                        z-index: 20;
                        &-cont {
                            padding: 8px 12px;
                            width: 200px;
                            text-align: center;
                            box-sizing: border-box;
                            min-height: 30px;
                            border-radius: 10px;
                            border: 2px solid #5a85ec;
                            background: #ffffffcc;
                            color: #323a57;
                        }
                        &-btn {
                            display: flex;
                            justify-content: space-between;
                            margin-top: 6px;
                            div {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 90px;
                                cursor: pointer;
                                height: 29px;
                                border-radius: 14.5px;
                                border: 2px solid #f25500;
                                background: #e98b00;
                                color: #ffffff;
                                font-size: 14px;
                                img {
                                    width: 16px;
                                    height: 16px;
                                    margin-right: 5px;
                                }
                            }
                        }
                    }
                }
                .position-right {
                    right: -100px;
                }
                .position-left {
                    left: -100px;
                }
                .top50 {
                    top: 58px;
                }
            }
        }
    }
}
</style>
  